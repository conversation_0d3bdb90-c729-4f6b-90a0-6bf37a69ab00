import Link from 'next/link';
import Image from 'next/image';

export default function ServicesPage() {
  const services = [
    {
      title: 'Pool Fencing',
      slug: 'pool-fencing',
      description: 'Professional pool fence installation to keep your family safe while maintaining the beauty of your backyard.',
      image: '/Images/Removable-Mesh-Pool-Fence-Toronto.jpg'
    },
    {
      title: 'Wood Fencing',
      slug: 'wood-fencing',
      description: 'Beautiful, durable wood fencing solutions that add natural charm and privacy to your property.',
      image: '/Images/wood-fence-installation-toronto.jpg'
    },
    {
      title: 'Vinyl Fencing',
      slug: 'vinyl-fencing',
      description: 'Low-maintenance vinyl fencing that offers durability and style without the upkeep of traditional materials.',
      image: '/Images/vinyl-fence-installation.jpg'
    },
    {
      title: 'Chain Link Fencing',
      slug: 'chain-link-fencing',
      description: 'Affordable and durable chain link fencing solutions for residential and commercial properties.',
      image: '/Images/chain-link-fence-installation-toronto.jpg'
    },
    {
      title: 'Wrought Iron Fencing',
      slug: 'wrought-iron-fencing',
      description: 'Elegant wrought iron fencing that combines security with sophisticated style.',
      image: '/Images/GTA-Fencing---The-Best-Fence-Contractor-in-Toronto.jpg'
    },
    {
      title: 'Perimeter Fencing',
      slug: 'perimeter-fencing',
      description: 'Complete perimeter fencing solutions to secure and define your property boundaries.',
      image: '/Images/Fence-installation-contractor-toronto.jpg'
    },
    {
      title: 'Security Fencing',
      slug: 'security-fencing',
      description: 'High-security fencing solutions designed to protect your property and assets.',
      image: '/Images/GTA-Fencing---The-Best-Fence-Contractor-in-Toronto-2.jpg'
    },
    {
      title: 'Industrial Chain Link Fencing',
      slug: 'industrial-chain-link-fencing',
      description: 'Heavy-duty industrial chain link fencing for commercial and industrial applications.',
      image: '/Images/chain-link-fence-contractor-toronto.jpg'
    },
    {
      title: 'Fence Maintenance',
      slug: 'fence-maintenance',
      description: 'Professional fence maintenance services to keep your fencing in optimal condition.',
      image: '/Images/Fence-repair.jpg'
    },
    {
      title: 'Fence Repair Services',
      slug: 'fence-repair-services',
      description: 'Expert fence repair services to restore your fencing to like-new condition.',
      image: '/Images/Fence-repair.jpg'
    }
  ];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="bg-deep-navy text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">Professional Fencing Services in the GTA</h1>
          <p className="text-lg md:text-xl mb-8 max-w-3xl mx-auto">
            From residential privacy fences to commercial security solutions, GTA Fencing Company provides comprehensive fencing services across the Greater Toronto Area.
          </p>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map(service => (
              <Link key={service.slug} href={`/services/${service.slug}`} className="block bg-white rounded-lg shadow-md hover:shadow-xl transition-shadow overflow-hidden">
                <div className="relative h-48">
                  <Image
                    src={service.image}
                    alt={service.title}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="p-6">
                  <h2 className="text-2xl font-bold text-deep-navy mb-3">{service.title}</h2>
                  <p className="text-rich-charcoal leading-relaxed">{service.description}</p>
                  <div className="mt-4 text-premium-gold font-semibold">
                    Learn More →
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-warm-off-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-deep-navy mb-4">Ready to Get Started?</h2>
          <p className="text-lg text-rich-charcoal mb-8">Contact us today for your free, no-obligation quote on any of our fencing services.</p>
          <Link href="/contact" className="inline-block bg-bright-orange-red text-white px-8 py-3 rounded-md font-semibold hover:bg-opacity-90 transition-all">
            Get Your Free Quote
          </Link>
        </div>
      </section>
    </div>
  );
}