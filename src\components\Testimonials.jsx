import Image from 'next/image';
import { Star, UserCircle, Quote } from 'lucide-react';

const Testimonials = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      location: "Mississauga, ON",
      rating: 5,
      text: "GTA Fencing transformed our backyard with a beautiful cedar fence. The team was professional, punctual, and the quality exceeded our expectations. Highly recommend!",
      project: "Cedar Privacy Fence"
    },
    {
      name: "<PERSON>",
      location: "Toronto, ON", 
      rating: 5,
      text: "Needed emergency fence repair after a storm. GTA Fencing responded quickly and had our chain link fence fixed the same day. Excellent service!",
      project: "Emergency Chain Link Repair"
    },
    {
      name: "<PERSON>",
      location: "Brampton, ON",
      rating: 5,
      text: "The pool fence installation was flawless. Safety was our top priority and GTA Fencing delivered exactly what we needed. Our kids can play safely now.",
      project: "Pool Safety Fence"
    }
  ];

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className="w-5 h-5 stroke-current"
        style={{
          color: i < rating ? 'var(--color-premium-gold)' : '#d1d5db',
          fill: i < rating ? 'var(--color-premium-gold)' : 'none',
          strokeWidth: 2
        }}
      />
    ));
  };

  return (
    <section className="py-16 bg-warm-off-white">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-deep-navy mb-4">What Our Customers Say</h2>
          <p className="text-lg text-rich-charcoal max-w-2xl mx-auto">
            Don't just take our word for it. Here's what homeowners and businesses across the GTA say about our fencing services.
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 group hover:scale-105">
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center">
                  {renderStars(testimonial.rating)}
                </div>
                <Quote
                  className="w-6 h-6 stroke-current fill-none opacity-50 group-hover:opacity-70 transition-opacity"
                  style={{color: 'var(--color-premium-gold)', strokeWidth: 2}}
                />
              </div>
              <p className="text-rich-charcoal mb-4 italic">"{testimonial.text}"</p>
              <div className="border-t pt-4">
                <div className="flex items-center mb-2">
                  <UserCircle
                    className="w-5 h-5 stroke-current fill-none mr-2"
                    style={{color: 'var(--color-premium-gold)', strokeWidth: 2}}
                  />
                  <p className="font-semibold text-deep-navy">{testimonial.name}</p>
                </div>
                <p className="text-sm text-gray-600 ml-7">{testimonial.location}</p>
                <p className="text-sm text-premium-gold font-medium ml-7">{testimonial.project}</p>
              </div>
            </div>
          ))}
        </div>
        
        <div className="text-center mt-8">
          <div className="inline-flex items-center bg-white px-6 py-3 rounded-full shadow-md hover:shadow-lg transition-shadow">
            <div className="flex mr-3">
              {Array.from({ length: 5 }, (_, i) => (
                <Star
                  key={i}
                  className="w-5 h-5 stroke-current"
                  style={{
                    color: 'var(--color-premium-gold)',
                    fill: 'var(--color-premium-gold)',
                    strokeWidth: 2
                  }}
                />
              ))}
            </div>
            <span className="text-deep-navy font-semibold">4.9/5 Average Rating</span>
            <span className="text-gray-600 ml-2">• 200+ Reviews</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;