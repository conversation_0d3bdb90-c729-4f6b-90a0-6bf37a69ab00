import Link from 'next/link';

const CTA = ({ text, link, isButton = false }) => {
  const baseClasses = "inline-block bg-bright-orange-red text-white px-8 py-4 rounded-lg font-bold text-lg text-center transition-all duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-premium-gold focus:ring-opacity-50 active:scale-95";

  if (isButton) {
    return (
      <button className={baseClasses}>
        {text}
      </button>
    );
  }

  return (
    <Link href={link} className={baseClasses}>
      {text}
    </Link>
  );
};

export default CTA;