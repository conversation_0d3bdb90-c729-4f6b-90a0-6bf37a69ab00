import Link from 'next/link';
import CTA from '@/components/CTA';

const locations = [
  {
    name: 'Toronto',
    slug: 'toronto',
    description: 'Professional fencing services throughout Toronto. Expert installation and repair with full compliance to municipal by-laws.',
    areas: ['North York', 'Scarborough', 'Etobicoke', 'Downtown Toronto']
  },
  {
    name: '<PERSON>',
    slug: 'vaughan',
    description: 'Premier fence installation and repair services for Vaughan. From Woodbridge to Kleinburg, we serve all communities.',
    areas: ['Woodbridge', 'Maple', 'Concord', 'Kleinburg']
  },
  {
    name: 'Richmond Hill',
    slug: 'richmond-hill',
    description: 'Top-rated fencing company in Richmond Hill. Quality solutions that respect the Oak Ridges Moraine regulations.',
    areas: ['Oak Ridges', 'Mill Pond', 'Jefferson', 'Bayview Hill']
  },
  {
    name: 'Mark<PERSON>',
    slug: 'markham',
    description: 'Expert fence building and repair services in Markham. Serving historic Unionville to modern Cornell developments.',
    areas: ['Unionville', 'Thornhill', 'Cornell', 'Angus Glen']
  },
  {
    name: 'Mississauga',
    slug: 'mississauga',
    description: 'Leading fence contractor in Mississauga. Complete fencing services from Port Credit to Streetsville.',
    areas: ['Port Credit', 'Streetsville', 'Clarkson', 'Erin Mills']
  },
  {
    name: 'Brampton',
    slug: 'brampton',
    description: 'Expert fence installation and repair in Brampton. Trusted local contractor serving all neighborhoods.',
    areas: ['Bramalea', 'Heart Lake', 'Mount Pleasant', 'Springdale']
  },
  {
    name: 'Oakville',
    slug: 'oakville',
    description: 'Premium fencing solutions for Oakville homes. Custom design and installation for prestigious neighborhoods.',
    areas: ['Bronte', 'Glen Abbey', 'Joshua Creek', 'Kerr Village']
  },
  {
    name: 'Burlington',
    slug: 'burlington',
    description: 'Quality fencing across Burlington. Professional installation from the lakefront to the Niagara Escarpment.',
    areas: ['Aldershot', 'Roseland', 'Millcroft', 'Downtown Burlington']
  },
  {
    name: 'Whitby',
    slug: 'whitby',
    description: 'Professional fence contractor serving Whitby. Complete fencing solutions for homes and businesses.',
    areas: ['Brooklin', 'Williamsburg', 'Pringle Creek', 'Port Whitby']
  },
  {
    name: 'Oshawa',
    slug: 'oshawa',
    description: 'Top-rated fence installation and repair in Oshawa. Reliable and affordable fencing services city-wide.',
    areas: ['Northwood', 'Samac', 'Lakeview', 'Windfields']
  }
];

export default function LocationsPage() {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="bg-deep-navy text-white py-16">
        <div className="container mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">Our GTA Service Areas</h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
            Professional fencing services across the Greater Toronto Area. Local expertise, 
            municipal compliance, and quality craftsmanship in every community we serve.
          </p>
          <CTA text="Get Your Free Quote" link="/contact" />
        </div>
      </section>

      {/* Locations Grid */}
      <section className="py-16">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {locations.map(location => (
              <div key={location.slug} className="bg-white border border-gray-200 rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300">
                <div className="p-8">
                  <h2 className="text-2xl font-bold text-deep-navy mb-4">{location.name}</h2>
                  <p className="text-rich-charcoal mb-6 leading-relaxed">{location.description}</p>
                  
                  <div className="mb-6">
                    <h3 className="text-sm font-semibold text-deep-navy mb-2 uppercase tracking-wide">Areas Served:</h3>
                    <div className="flex flex-wrap gap-2">
                      {location.areas.map((area, index) => (
                        <span key={index} className="bg-soft-beige text-rich-charcoal px-3 py-1 rounded-full text-sm">
                          {area}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  <Link 
                    href={`/locations/${location.slug}`} 
                    className="inline-block bg-warm-gold text-deep-navy px-6 py-3 rounded-lg font-semibold hover:bg-deep-navy hover:text-white transition-colors duration-200"
                  >
                    Learn More
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16 bg-warm-off-white">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-deep-navy text-center mb-12">Why Choose GTA Fencing Company?</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-deep-navy text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">1</span>
              </div>
              <h3 className="text-xl font-bold text-deep-navy mb-4">Local Expertise</h3>
              <p className="text-rich-charcoal">
                Deep knowledge of municipal by-laws, permit requirements, and local regulations 
                across all GTA communities.
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-deep-navy text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">2</span>
              </div>
              <h3 className="text-xl font-bold text-deep-navy mb-4">Quality Materials</h3>
              <p className="text-rich-charcoal">
                Premium fencing materials designed to withstand Southern Ontario&apos;s demanding
                weather conditions and provide lasting value.
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-deep-navy text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">3</span>
              </div>
              <h3 className="text-xl font-bold text-deep-navy mb-4">Professional Service</h3>
              <p className="text-rich-charcoal">
                From consultation to cleanup, we provide white-glove service with prompt 
                response times and reliable project completion.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-deep-navy text-white text-center">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold mb-4">Ready to Start Your Fencing Project?</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto">
            Contact us today for your free, no-obligation quote. Our local experts are ready 
            to help you with all your fencing needs across the GTA.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <CTA text="Get Free Quote" link="/contact" />
            <Link 
              href="/services" 
              className="bg-transparent border-2 border-warm-gold text-warm-gold px-8 py-3 rounded-lg font-semibold hover:bg-warm-gold hover:text-deep-navy transition-colors duration-200"
            >
              View All Services
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}

// Generate metadata for SEO
export const metadata = {
  title: 'GTA Fencing Company Service Areas - Professional Fence Installation Across the GTA',
  description: 'Professional fencing services across the Greater Toronto Area. Expert installation and repair in Toronto, Vaughan, Richmond Hill, Markham, Mississauga, Brampton, Oakville, Burlington, Whitby, and Oshawa.',
};
