'use client';

import { useState } from 'react';

const FAQ = () => {
  const [openIndex, setOpenIndex] = useState(null);

  const faqs = [
    {
      question: "How long does a typical fence installation take?",
      answer: "Most residential fence installations take 1-3 days depending on the size and complexity. We'll provide a detailed timeline during your free consultation."
    },
    {
      question: "Do I need a permit for my fence installation?",
      answer: "Permit requirements vary by municipality. Our team is familiar with local regulations across the GTA and will help you obtain any necessary permits."
    },
    {
      question: "What's included in your free estimate?",
      answer: "Our free estimate includes a site visit, measurements, material recommendations, labor costs, and a detailed project timeline. No hidden fees, ever."
    },
    {
      question: "Do you offer warranty on your work?",
      answer: "Yes! We provide a comprehensive warranty on both materials and workmanship. Specific terms vary by project type - we'll explain everything during your consultation."
    },
    {
      question: "Can you repair existing fences?",
      answer: "Absolutely! We offer emergency repairs, routine maintenance, and complete fence restoration services for all fence types across the GTA."
    },
    {
      question: "What fence materials do you recommend for Ontario weather?",
      answer: "We recommend materials specifically chosen for Southern Ontario's climate - cedar for natural beauty, vinyl for low maintenance, and galvanized steel for durability."
    },
    {
      question: "How do I prepare my property for fence installation?",
      answer: "We'll provide a detailed preparation checklist, but generally you'll need to mark underground utilities and ensure access to the work area. We handle the rest!"
    },
    {
      question: "Do you provide emergency fence repair services?",
      answer: "Yes! We offer 24/7 emergency repair services for storm damage, security breaches, or safety concerns. Call us anytime at (*************."
    }
  ];

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto max-w-4xl">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-deep-navy mb-4">Frequently Asked Questions</h2>
          <p className="text-lg text-rich-charcoal">
            Get answers to common questions about our fencing services in the Greater Toronto Area.
          </p>
        </div>

        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
              <button
                className="w-full px-6 py-4 text-left bg-soft-beige hover:bg-premium-gold hover:text-white transition-colors focus:outline-none focus:ring-2 focus:ring-premium-gold"
                onClick={() => toggleFAQ(index)}
              >
                <div className="flex justify-between items-center">
                  <h3 className="font-semibold text-lg">{faq.question}</h3>
                  <span className="text-2xl font-light">
                    {openIndex === index ? '−' : '+'}
                  </span>
                </div>
              </button>
              {openIndex === index && (
                <div className="px-6 py-4 bg-white border-t border-gray-200">
                  <p className="text-rich-charcoal leading-relaxed">{faq.answer}</p>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="text-center mt-8">
          <p className="text-rich-charcoal mb-4">Still have questions?</p>
          <a 
            href="tel:************" 
            className="inline-block bg-bright-orange-red text-white px-6 py-3 rounded-lg font-semibold hover:bg-opacity-90 transition-all"
          >
            Call Us: (*************
          </a>
        </div>
      </div>
    </section>
  );
};

export default FAQ;