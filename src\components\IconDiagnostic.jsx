import { Calendar, Users, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Star } from 'lucide-react';

const IconDiagnostic = () => {
  return (
    <div className="p-8 bg-white">
      <h2 className="text-2xl font-bold mb-6">Icon Diagnostic Test</h2>
      
      {/* Test 1: Basic Lucide Icons */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Test 1: Basic Lucide Icons</h3>
        <div className="flex gap-4">
          <Calendar className="w-8 h-8 text-red-500" />
          <Users className="w-8 h-8 text-blue-500" />
          <ShieldCheck className="w-8 h-8 text-green-500" />
          <Clock className="w-8 h-8 text-purple-500" />
          <Star className="w-8 h-8 text-yellow-500" />
        </div>
      </div>

      {/* Test 2: Icons with size prop */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Test 2: Icons with size prop</h3>
        <div className="flex gap-4">
          <Calendar size={32} className="text-red-500" />
          <Users size={32} className="text-blue-500" />
          <ShieldCheck size={32} className="text-green-500" />
          <Clock size={32} className="text-purple-500" />
          <Star size={32} className="text-yellow-500" />
        </div>
      </div>

      {/* Test 3: Icons with inline styles */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Test 3: Icons with inline styles</h3>
        <div className="flex gap-4">
          <Calendar size={32} style={{color: '#D4A537', strokeWidth: 2}} />
          <Users size={32} style={{color: '#D4A537', strokeWidth: 2}} />
          <ShieldCheck size={32} style={{color: '#D4A537', strokeWidth: 2}} />
          <Clock size={32} style={{color: '#D4A537', strokeWidth: 2}} />
          <Star size={32} style={{color: '#D4A537', strokeWidth: 2}} />
        </div>
      </div>

      {/* Test 4: Icons with CSS variables */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Test 4: Icons with CSS variables</h3>
        <div className="flex gap-4">
          <Calendar size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
          <Users size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
          <ShieldCheck size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
          <Clock size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
          <Star size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
        </div>
      </div>

      {/* Test 5: Icons in circles (BROKEN - current implementation) */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Test 5: Icons in circles (BROKEN)</h3>
        <div className="flex gap-4">
          <div className="bg-premium-gold bg-opacity-20 p-4 rounded-full">
            <Calendar size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
          </div>
          <div className="bg-premium-gold bg-opacity-20 p-4 rounded-full">
            <Users size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
          </div>
          <div className="bg-premium-gold bg-opacity-20 p-4 rounded-full">
            <ShieldCheck size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
          </div>
          <div className="bg-premium-gold bg-opacity-20 p-4 rounded-full">
            <Clock size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
          </div>
        </div>
      </div>

      {/* Test 7: Fixed circles with relative positioning */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Test 7: Fixed circles with relative positioning</h3>
        <div className="flex gap-4">
          <div className="relative bg-premium-gold bg-opacity-20 p-4 rounded-full">
            <Calendar size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} className="relative z-10" />
          </div>
          <div className="relative bg-premium-gold bg-opacity-20 p-4 rounded-full">
            <Users size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} className="relative z-10" />
          </div>
          <div className="relative bg-premium-gold bg-opacity-20 p-4 rounded-full">
            <ShieldCheck size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} className="relative z-10" />
          </div>
          <div className="relative bg-premium-gold bg-opacity-20 p-4 rounded-full">
            <Clock size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} className="relative z-10" />
          </div>
        </div>
      </div>

      {/* Test 8: Alternative circle styling */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Test 8: Alternative circle styling</h3>
        <div className="flex gap-4">
          <div className="w-16 h-16 bg-premium-gold bg-opacity-20 rounded-full flex items-center justify-center">
            <Calendar size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
          </div>
          <div className="w-16 h-16 bg-premium-gold bg-opacity-20 rounded-full flex items-center justify-center">
            <Users size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
          </div>
          <div className="w-16 h-16 bg-premium-gold bg-opacity-20 rounded-full flex items-center justify-center">
            <ShieldCheck size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
          </div>
          <div className="w-16 h-16 bg-premium-gold bg-opacity-20 rounded-full flex items-center justify-center">
            <Clock size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
          </div>
        </div>
      </div>

      {/* Test 9: Border instead of background */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Test 9: Border instead of background</h3>
        <div className="flex gap-4">
          <div className="w-16 h-16 border-2 border-premium-gold border-opacity-40 rounded-full flex items-center justify-center">
            <Calendar size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
          </div>
          <div className="w-16 h-16 border-2 border-premium-gold border-opacity-40 rounded-full flex items-center justify-center">
            <Users size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
          </div>
          <div className="w-16 h-16 border-2 border-premium-gold border-opacity-40 rounded-full flex items-center justify-center">
            <ShieldCheck size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
          </div>
          <div className="w-16 h-16 border-2 border-premium-gold border-opacity-40 rounded-full flex items-center justify-center">
            <Clock size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
          </div>
        </div>
      </div>

      {/* Test 6: Raw SVG for comparison */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Test 6: Raw SVG for comparison</h3>
        <div className="flex gap-4">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#D4A537" strokeWidth="2">
            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
            <line x1="16" y1="2" x2="16" y2="6"/>
            <line x1="8" y1="2" x2="8" y2="6"/>
            <line x1="3" y1="10" x2="21" y2="10"/>
          </svg>
        </div>
      </div>
    </div>
  );
};

export default IconDiagnostic;