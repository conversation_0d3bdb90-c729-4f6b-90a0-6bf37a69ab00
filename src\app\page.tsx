import CTA from "@/components/CTA";
import Testimonials from "@/components/Testimonials";
import TrustIndicators from "@/components/TrustIndicators";
import FAQ from "@/components/FAQ";
import Link from "next/link";
import { Waves, TreePine, Home as HomeIcon, Link as LinkIcon, DoorOpen, Shield, Wrench, Building, Settings, Gem, Calculator, BadgeCheck, Award, Handshake, Heart, ShieldCheck, CheckCircle } from 'lucide-react';

export default function Home() {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="bg-deep-navy text-white text-center py-20">
        <div className="container mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">Your Premier Fence Contractor in the Greater Toronto Area</h1>
          <p className="text-lg md:text-xl mb-8 max-w-3xl mx-auto">
            As the GTA&apos;s leading fencing specialists, GTA Fencing Company provides expert installation, repair, and maintenance for homeowners and businesses across Toronto, Mississauga, Brampton, and the surrounding areas. We specialize in a wide range of fencing solutions designed for Southern Ontario&apos;s climate. Get your free, no-obligation quote today.
          </p>
          <CTA text="Get Your Free Quote" link="/contact" />
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-warm-off-white">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl font-bold text-deep-navy mb-8">Our Fencing Services</h2>
          <p className="text-lg text-rich-charcoal mb-8 max-w-2xl mx-auto">
            This section lists and links to all primary service pages, establishing the breadth of expertise.
          </p>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8">
            <Link href="/services/pool-fencing" className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 group hover:scale-105">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-3 group-hover:border-opacity-50 transition-all duration-300">
                  <Waves size={24} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="font-bold text-rich-charcoal group-hover:text-premium-gold transition-colors">Pool Fencing</h3>
              </div>
            </Link>
            
            <Link href="/services/wood-fencing" className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 group hover:scale-105">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-3 group-hover:border-opacity-50 transition-all duration-300">
                  <TreePine size={24} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="font-bold text-rich-charcoal group-hover:text-premium-gold transition-colors">Wood Fencing</h3>
              </div>
            </Link>
            
            <Link href="/services/vinyl-fencing" className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 group hover:scale-105">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-3 group-hover:border-opacity-50 transition-all duration-300">
                  <HomeIcon size={24} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="font-bold text-rich-charcoal group-hover:text-premium-gold transition-colors">Vinyl Fencing</h3>
              </div>
            </Link>
            
            <Link href="/services/chain-link-fencing" className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 group hover:scale-105">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-3 group-hover:border-opacity-50 transition-all duration-300">
                  <LinkIcon size={24} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="font-bold text-rich-charcoal group-hover:text-premium-gold transition-colors">Chain Link Fencing</h3>
              </div>
            </Link>
            
            <Link href="/services/wrought-iron-fencing" className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 group hover:scale-105">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-3 group-hover:border-opacity-50 transition-all duration-300">
                  <DoorOpen size={24} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="font-bold text-rich-charcoal group-hover:text-premium-gold transition-colors">Wrought Iron Fencing</h3>
              </div>
            </Link>
            
            <Link href="/services/perimeter-fencing" className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 group hover:scale-105">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-3 group-hover:border-opacity-50 transition-all duration-300">
                  <Shield size={24} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="font-bold text-rich-charcoal group-hover:text-premium-gold transition-colors">Perimeter Fencing</h3>
              </div>
            </Link>
            
            <Link href="/services/security-fencing" className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 group hover:scale-105">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-3 group-hover:border-opacity-50 transition-all duration-300">
                  <Shield size={24} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="font-bold text-rich-charcoal group-hover:text-premium-gold transition-colors">Security Fencing</h3>
              </div>
            </Link>
            
            <Link href="/services/industrial-chain-link-fencing" className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 group hover:scale-105">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-3 group-hover:border-opacity-50 transition-all duration-300">
                  <Building size={24} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="font-bold text-rich-charcoal group-hover:text-premium-gold transition-colors">Industrial Chain Link Fencing</h3>
              </div>
            </Link>
            
            <Link href="/services/fence-maintenance" className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 group hover:scale-105">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-3 group-hover:border-opacity-50 transition-all duration-300">
                  <Settings size={24} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="font-bold text-rich-charcoal group-hover:text-premium-gold transition-colors">Fence Maintenance</h3>
              </div>
            </Link>
            
            <Link href="/services/fence-repair-services" className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 group hover:scale-105">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-3 group-hover:border-opacity-50 transition-all duration-300">
                  <Wrench size={24} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="font-bold text-rich-charcoal group-hover:text-premium-gold transition-colors">Fence Repair Services</h3>
              </div>
            </Link>
          </div>
        </div>
      </section>

      {/* Locations Section */}
      <section className="py-16">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl font-bold text-deep-navy mb-8">Serving the Greater Toronto Area and Surrounding Communities</h2>
          <p className="text-lg text-rich-charcoal mb-8 max-w-2xl mx-auto">
            This section lists and links to all primary location pages, establishing local relevance.
          </p>
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            {['Toronto', 'Vaughan', 'Richmond Hill', 'Markham', 'Mississauga', 'Brampton', 'Oakville', 'Burlington', 'Whitby', 'Oshawa'].map(location => (
              <Link key={location} href={`/locations/${location.toLowerCase()}`} className="bg-soft-beige px-4 py-2 rounded-full text-deep-navy font-semibold hover:bg-premium-gold hover:text-white transition-colors">
                {location}
              </Link>
            ))}
          </div>
          
          {/* Local SEO Enhancement */}
          <div className="bg-soft-beige p-6 rounded-lg max-w-4xl mx-auto">
            <h3 className="text-xl font-bold text-deep-navy mb-4">Proudly Serving Southern Ontario</h3>
            <p className="text-rich-charcoal">
              From the bustling streets of downtown Toronto to the quiet neighborhoods of Oakville, we bring professional fencing solutions to every corner of the Greater Toronto Area. Our local expertise means we understand the unique challenges of Ontario weather and municipal requirements.
            </p>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16 bg-soft-beige">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-deep-navy text-center mb-8">Why Choose GTA Fencing Company?</h2>
          <p className="text-lg text-rich-charcoal text-center mb-12 max-w-2xl mx-auto">
            This section leverages the defined trust-building elements to build confidence and reduce user anxiety.
          </p>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center flex flex-col items-center group">
              <div className="w-16 h-16 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-4 group-hover:border-opacity-50 transition-all duration-300">
                <Settings size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
              </div>
              <h3 className="text-xl font-bold text-rich-charcoal mb-2">Expert Installation</h3>
              <p className="text-rich-charcoal">Our certified team ensures every fence is built to last, meeting all local codes and standards.</p>
            </div>
            <div className="text-center flex flex-col items-center group">
              <div className="w-16 h-16 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-4 group-hover:border-opacity-50 transition-all duration-300">
                <Gem size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
              </div>
              <h3 className="text-xl font-bold text-rich-charcoal mb-2">Quality Materials and Workmanship</h3>
              <p className="text-rich-charcoal">We source premium materials and stand behind our work, guaranteeing a durable and beautiful result.</p>
            </div>
            <div className="text-center flex flex-col items-center group">
              <div className="w-16 h-16 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-4 group-hover:border-opacity-50 transition-all duration-300">
                <Calculator size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
              </div>
              <h3 className="text-xl font-bold text-rich-charcoal mb-2">Free, No-Obligation Quotes</h3>
              <p className="text-rich-charcoal">We provide clear, detailed estimates with no hidden fees, helping you make an informed decision.</p>
            </div>
            <div className="text-center flex flex-col items-center group">
              <div className="w-16 h-16 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-4 group-hover:border-opacity-50 transition-all duration-300">
                <BadgeCheck size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
              </div>
              <h3 className="text-xl font-bold text-rich-charcoal mb-2">Licensed and Insured</h3>
              <p className="text-rich-charcoal">Work with confidence knowing our team is fully licensed and insured for your complete protection.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Trust Indicators Section */}
      <TrustIndicators />

      {/* Testimonials Section */}
      <Testimonials />

      {/* Story Section */}
      <section className="py-16">
        <div className="container mx-auto text-center max-w-4xl">
          <h2 className="text-3xl font-bold text-deep-navy mb-6">Our Story of Quality and Commitment</h2>
          <div className="text-left space-y-6">
            <p className="text-lg text-rich-charcoal leading-relaxed">
              Founded in 2009, GTA Fencing Company began with a simple mission: to provide the Greater Toronto Area with exceptional fencing solutions that combine quality craftsmanship with outstanding customer service. What started as a small family business has grown into the region&apos;s most trusted fencing contractor.
            </p>
            <p className="text-lg text-rich-charcoal leading-relaxed">
              Our commitment to the local community runs deep. We&apos;ve helped thousands of homeowners create safe, beautiful outdoor spaces while supporting local businesses and contributing to the economic growth of the GTA. Every fence we install is a testament to our dedication to quality and our investment in the communities we serve.
            </p>
            <p className="text-lg text-rich-charcoal leading-relaxed">
              Today, we continue to uphold the values that built our reputation: integrity, craftsmanship, and an unwavering commitment to customer satisfaction. When you choose GTA Fencing Company, you&apos;re not just getting a fence – you&apos;re partnering with a local business that truly cares about your project and your community.
            </p>
          </div>
          
          {/* Company Values */}
          <div className="grid md:grid-cols-3 gap-6 mt-12">
            <div className="bg-soft-beige p-6 rounded-lg group hover:shadow-lg transition-all duration-300">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 border-2 border-premium-gold border-opacity-40 rounded-full flex items-center justify-center mr-3 group-hover:border-opacity-60 transition-all duration-300">
                  <Award size={20} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="text-xl font-bold text-deep-navy">Excellence</h3>
              </div>
              <p className="text-rich-charcoal">We never compromise on quality, using only the finest materials and proven installation techniques.</p>
            </div>
            <div className="bg-soft-beige p-6 rounded-lg group hover:shadow-lg transition-all duration-300">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 border-2 border-premium-gold border-opacity-40 rounded-full flex items-center justify-center mr-3 group-hover:border-opacity-60 transition-all duration-300">
                  <Handshake size={20} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="text-xl font-bold text-deep-navy">Integrity</h3>
              </div>
              <p className="text-rich-charcoal">Honest pricing, transparent communication, and reliable service you can count on.</p>
            </div>
            <div className="bg-soft-beige p-6 rounded-lg group hover:shadow-lg transition-all duration-300">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 border-2 border-premium-gold border-opacity-40 rounded-full flex items-center justify-center mr-3 group-hover:border-opacity-60 transition-all duration-300">
                  <Heart size={20} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="text-xl font-bold text-deep-navy">Community</h3>
              </div>
              <p className="text-rich-charcoal">Proudly supporting local families and businesses throughout the Greater Toronto Area.</p>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <FAQ />

      {/* Guarantee Section */}
      <section className="py-16 bg-soft-beige">
        <div className="container mx-auto text-center max-w-4xl">
          <h2 className="text-3xl font-bold text-deep-navy mb-8">Our Comprehensive Guarantee</h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-white p-8 rounded-lg shadow-md group hover:shadow-xl transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="w-16 h-16 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mr-4 group-hover:border-opacity-50 transition-all duration-300">
                  <ShieldCheck size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="text-2xl font-bold text-premium-gold">Workmanship Warranty</h3>
              </div>
              <p className="text-rich-charcoal mb-4">
                We stand behind every installation with our comprehensive workmanship warranty. If any issues arise due to installation defects, we&apos;ll fix them at no cost to you.
              </p>
              <ul className="text-left text-rich-charcoal space-y-2">
                <li className="flex items-center">
                  <CheckCircle size={16} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} className="mr-2" /> 5-year workmanship guarantee
                </li>
                <li className="flex items-center">
                  <CheckCircle size={16} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} className="mr-2" /> Free repairs for installation defects
                </li>
                <li className="flex items-center">
                  <CheckCircle size={16} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} className="mr-2" /> 24/7 emergency service available
                </li>
              </ul>
            </div>
            
            <div className="bg-white p-8 rounded-lg shadow-md group hover:shadow-xl transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="w-16 h-16 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mr-4 group-hover:border-opacity-50 transition-all duration-300">
                  <CheckCircle size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="text-2xl font-bold text-premium-gold">Satisfaction Promise</h3>
              </div>
              <p className="text-rich-charcoal mb-4">
                Your complete satisfaction is our priority. We&apos;re not finished until you&apos;re 100% happy with your new fence.
              </p>
              <ul className="text-left text-rich-charcoal space-y-2">
                <li className="flex items-center">
                  <CheckCircle
                    className="w-4 h-4 stroke-current fill-none mr-2"
                    style={{color: 'var(--color-premium-gold)', strokeWidth: 2}}
                  /> 100% satisfaction guarantee
                </li>
                <li className="flex items-center">
                  <CheckCircle
                    className="w-4 h-4 stroke-current fill-none mr-2"
                    style={{color: 'var(--color-premium-gold)', strokeWidth: 2}}
                  /> Free consultations and estimates
                </li>
                <li className="flex items-center">
                  <CheckCircle
                    className="w-4 h-4 stroke-current fill-none mr-2"
                    style={{color: 'var(--color-premium-gold)', strokeWidth: 2}}
                  /> Clear communication throughout
                </li>
              </ul>
            </div>
          </div>
          
          <div className="mt-8 bg-deep-navy text-white p-6 rounded-lg">
            <h3 className="text-xl font-bold mb-2">Material Warranties Included</h3>
            <p>All materials come with manufacturer warranties ranging from 10-25 years depending on the product. We&apos;ll help you understand and utilize these warranties if needed.</p>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-premium-gold text-center">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold mb-4 text-deep-navy">Get Your Free Fencing Estimate Today</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto text-deep-navy">
            Ready to transform your property with a beautiful, durable fence? Contact us today for your free, no-obligation consultation and detailed estimate.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <CTA text="Request Your Free Quote" link="/contact" />
            <a 
              href="tel:************" 
              className="inline-block bg-deep-navy text-warm-off-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-opacity-90 transition-all"
            >
              Call: (*************
            </a>
          </div>
          <p className="text-sm mt-6 text-deep-navy">
            Licensed • Insured • Locally Owned • Serving the GTA Since 2009
          </p>
        </div>
      </section>
    </div>
  );
}
