import Image from 'next/image';

const TrustBadge = ({ imageUrl, text, icon: IconComponent, iconColor = "var(--color-premium-gold)" }) => {
  return (
    <div className="flex items-center space-x-3 bg-soft-beige p-4 rounded-lg hover:shadow-md transition-all duration-300 group">
      {IconComponent ? (
        <div className="bg-premium-gold bg-opacity-10 p-2 rounded-full group-hover:bg-opacity-20 transition-all duration-300">
          <IconComponent
            className="w-6 h-6 stroke-current fill-none"
            style={{color: iconColor, strokeWidth: 2}}
          />
        </div>
      ) : imageUrl ? (
        <Image src={imageUrl} alt={text} width={40} height={40} className="rounded-full" />
      ) : null}
      <span className="font-semibold text-deep-navy group-hover:text-premium-gold transition-colors">{text}</span>
    </div>
  );
};

export default TrustBadge;