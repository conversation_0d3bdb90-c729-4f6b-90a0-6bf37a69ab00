import { Calendar, Users, ShieldChe<PERSON>, Clock } from 'lucide-react';

const TrustIndicators = () => {

  return (
    <section className="py-16 bg-deep-navy text-white">
      <div className="container mx-auto">
        {/* Stats Section */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">Trusted by Thousands Across the GTA</h2>
          <p className="text-lg text-warm-off-white max-w-2xl mx-auto">
            Our track record speaks for itself. We've been building trust one fence at a time.
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
          <div className="text-center group">
            <div className="flex justify-center mb-4">
              <div className="w-20 h-20 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center group-hover:border-opacity-50 transition-all duration-300">
                <Calendar size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
              </div>
            </div>
            <div className="text-4xl md:text-5xl font-bold text-premium-gold mb-2 group-hover:scale-105 transition-transform duration-300">
              15+
            </div>
            <div className="text-xl font-semibold mb-1">Years in Business</div>
            <div className="text-sm text-warm-off-white">Serving the GTA since 2009</div>
          </div>
          
          <div className="text-center group">
            <div className="flex justify-center mb-4">
              <div className="w-20 h-20 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center group-hover:border-opacity-50 transition-all duration-300">
                <Users size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
              </div>
            </div>
            <div className="text-4xl md:text-5xl font-bold text-premium-gold mb-2 group-hover:scale-105 transition-transform duration-300">
              2,500+
            </div>
            <div className="text-xl font-semibold mb-1">Projects Completed</div>
            <div className="text-sm text-warm-off-white">Residential & Commercial</div>
          </div>
          
          <div className="text-center group">
            <div className="flex justify-center mb-4">
              <div className="w-20 h-20 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center group-hover:border-opacity-50 transition-all duration-300">
                <ShieldCheck size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
              </div>
            </div>
            <div className="text-4xl md:text-5xl font-bold text-premium-gold mb-2 group-hover:scale-105 transition-transform duration-300">
              100%
            </div>
            <div className="text-xl font-semibold mb-1">Licensed & Insured</div>
            <div className="text-sm text-warm-off-white">WSIB & Liability Coverage</div>
          </div>
          
          <div className="text-center group">
            <div className="flex justify-center mb-4">
              <div className="w-20 h-20 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center group-hover:border-opacity-50 transition-all duration-300">
                <Clock size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
              </div>
            </div>
            <div className="text-4xl md:text-5xl font-bold text-premium-gold mb-2 group-hover:scale-105 transition-transform duration-300">
              24/7
            </div>
            <div className="text-xl font-semibold mb-1">Emergency Service</div>
            <div className="text-sm text-warm-off-white">Storm damage & repairs</div>
          </div>
        </div>

        {/* Guarantee Section */}
        <div className="text-center mt-12">
          <div className="inline-block bg-bright-orange-red px-8 py-4 rounded-lg">
            <h3 className="text-xl font-bold mb-2">100% Satisfaction Guarantee</h3>
            <p className="text-sm">We stand behind every fence we build with our comprehensive warranty</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TrustIndicators;