import { Calendar, Users, <PERSON><PERSON>he<PERSON>, <PERSON>, Star } from 'lucide-react';

const IconTest = () => {
  return (
    <div className="p-8 bg-white">
      <h2 className="text-2xl font-bold mb-4">Icon Test</h2>
      <div className="flex gap-4 items-center">
        <div className="p-2 border">
          <Calendar size={24} color="red" />
          <p>Calendar</p>
        </div>
        <div className="p-2 border">
          <Users size={24} color="blue" />
          <p>Users</p>
        </div>
        <div className="p-2 border">
          <ShieldCheck size={24} color="green" />
          <p>ShieldCheck</p>
        </div>
        <div className="p-2 border">
          <Clock size={24} color="purple" />
          <p>Clock</p>
        </div>
        <div className="p-2 border">
          <Star size={24} color="orange" />
          <p>Star</p>
        </div>
      </div>
    </div>
  );
};

export default IconTest;